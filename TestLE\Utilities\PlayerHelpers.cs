using System.Collections;
using Il2Cpp;
using MelonLoader;
using UnityEngine;
using UnityEngine.AI;

namespace TestLE.Utilities;

public static class PlayerHelpers
{
    public static bool IsAbilityOnCooldown(int index)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return false;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return false;
        }

        return PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, index);
    }
    
    public static string? GetAbilityName(int index)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return null;
        }

        if (PLAYER.usingAbilityState != null)
            return PLAYER.usingAbilityState.abilityList.getAbility(index).abilityName;
        
        MelonLogger.Msg("usingAbilityState is null");
        return null;

    }

    public static void UseAbility(int index, Transform targetTransform)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return;
        }

        PLAYER.usingAbilityState.UseBufferableAbilityCommand(BufferableAbilityType.Bar, index, targetTransform.position, targetTransform, true, true, true, true);
    }

    public static void UseMovementAbility(Transform targetTransform)
    {
        if (CURRENT_ROUTINE == null || CURRENT_ROUTINE.MovementSkillIndex == -1 || IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
            return;

        UseAbility(CURRENT_ROUTINE.MovementSkillIndex, targetTransform);
    }

    public static void UseMovementAbility(Vector3 position)
    {
        if (CURRENT_ROUTINE == null || CURRENT_ROUTINE.MovementSkillIndex == -1 || IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
            return;

        PLAYER.usingAbilityState.UseBufferableAbilityCommand(BufferableAbilityType.Bar, CURRENT_ROUTINE.MovementSkillIndex, position, null, true, true, true, true);
    }

    public static void UsePortal()
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return;
        }

        PLAYER.usingAbilityState.UsePortalCommand(Vector3.zero, true);
    }

    public static bool UsePotion()
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return false;
        }

        if (PLAYER.healthPotion.currentCharges <= 0)
        {
            MelonLogger.Msg("No potions available");
            return false;
        }

        PLAYER.PotionKeyPressed();
        return true;
    }

    /// <summary>
    /// Will try once to move to the position.
    /// </summary>
    /// <param name="position">Position to move to</param>
    public static void MoveTo(Vector3 position)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        Move(position);
    }

    /// <summary>
    /// Will force the player to move to the position, looping until the player is within the stoppingDistance or maxTries is reached.
    /// </summary>
    /// <param name="position">Position to move to</param>
    /// <param name="stoppingDistance">Distance to stop moving</param>
    /// <param name="maxTries">Max number of tries</param>
    /// <param name="delayBetweentries">Delay between tries</param>
    /// <returns>IEnumerator for coroutine</returns>
    public static IEnumerator MoveToForce(Vector3 position, float stoppingDistance = 1f, int maxTries = 15, float delayBetweentries = 0.3333f)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            yield break;
        }

        for (var i = 0; i < maxTries; i++)
        {
            if (Vector3.Distance(PLAYER.transform.position, position) <= stoppingDistance)
                break;

            Move(position);
            yield return new WaitForSeconds(delayBetweentries);
        }
    }

    private static void Move(Vector3 position)
    {
        // Consistent null checking like other methods in this class
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.movingState == null)
        {
            MelonLogger.Msg("movingState is null");
            return;
        }

        if (PLAYER.transform == null)
        {
            MelonLogger.Msg("PLAYER transform is null");
            return;
        }

        // Constants for movement behavior
        const float navMeshSampleDistance = 15f;
        const float movementAbilityMinDistance = 8f;
        const float movementAbilityMaxDistance = 25f;

        // Find a valid position on the NavMesh
        if (!NavMesh.SamplePosition(position, out var hit, navMeshSampleDistance, -1))
        {
            MelonLogger.Msg("Failed to find a valid NavMesh position, using direct movement");
            // Fallback to direct movement if NavMesh sampling fails
            UseMouseClickMovement(position);
            return;
        }

        var targetPosition = hit.position;
        var distanceToTarget = Vector3.Distance(PLAYER.transform.position, targetPosition);

        // Use movement ability for medium to long distances if available and not on cooldown
        if (distanceToTarget >= movementAbilityMinDistance &&
            distanceToTarget <= movementAbilityMaxDistance &&
            CURRENT_ROUTINE != null &&
            CURRENT_ROUTINE.MovementSkillIndex != -1 &&
            !IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
        {
            UseMovementAbility(targetPosition);
            return;
        }

        // Use the most reliable Last Epoch movement function - MouseClickMoveCommand
        // This simulates exactly what a player would do when clicking to move
        UseMouseClickMovement(targetPosition);
    }

    /// <summary>
    /// Uses the most reliable Last Epoch movement function - MouseClickMoveCommand.
    /// This simulates exactly what a player would do when clicking to move.
    /// </summary>
    /// <param name="position">World position to move to</param>
    private static void UseMouseClickMovement(Vector3 position)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.movingState == null)
        {
            MelonLogger.Msg("movingState is null");
            return;
        }

        if (CAMERA == null)
        {
            MelonLogger.Msg("CAMERA is null, falling back to MoveToPointNoChecks");
            PLAYER.movingState.MoveToPointNoChecks(position, true);
            return;
        }

        // Use MouseClickMoveCommand - the most reliable movement method in Last Epoch
        // This simulates a mouse click movement command exactly like a player would do
        var ray = CAMERA.ScreenPointToRay(CAMERA.WorldToScreenPoint(position));
        PLAYER.movingState.MouseClickMoveCommand(ray, false, -1f, true, position, true);
    }

    /// <summary>
    /// Fallback movement function using MoveToPointNoChecks.
    /// Used when MouseClickMoveCommand is not available.
    /// </summary>
    /// <param name="position">World position to move to</param>
    private static void InternalMove(Vector3 position)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.movingState == null)
        {
            MelonLogger.Msg("movingState is null");
            return;
        }

        PLAYER.movingState.MoveToPointNoChecks(position, true);
    }

    // public static void StoreMaterials()
    // {
    //     if (STORE_MATERIALS_BUTTON == null)
    //     {
    //         MelonLogger.Msg("STORE_MATERIALS_BUTTON is null");
    //         return;
    //     }
    //
    //     STORE_MATERIALS_BUTTON.onClick.Invoke();
    // }
}
